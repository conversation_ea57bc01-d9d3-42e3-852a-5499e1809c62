// API 基础 URL
const HOST = 'http://**************:39050/agent'; // 替换为实际 URL
//const HOST = 'https://shuiyuzui-dev.bdnrc.org.cn/agent';
const AGENT_ORDER_STREAM_API = '/api/v1/order/stream';
const AGENT_DIALOG_STREAM_API = '/api/v1/dialog/stream';
const AGENT_QUERY_ITEMS_API = '/api/v1/query/items'
const AGENT_RESET_HISTORY_API = '/api/v1/dialog/reset'
const AGENT_RELATED_CONTENT_API = '/api/v1/related_content'
const AGENT_RECOMMENDATION_QUESTIONS_API = '/api/v1/recommendation_question'
const AGENT_KNOWLEDGE_TEST_QUESTIONS_API = '/api/v1/questions'
const TOKEN = 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiIyOjEwNDEwODYyODE1NTAzMTE0MjQiLCJyblN0ciI6Ikw5Y0lNY0ZjZklQdFZVV1Q1RDc0NTNTVUZCS1ppdDAyIiwiaWF0IjoxNzM2OTMwMzg3LCJpc3MiOiJCRE5SQy5PUkcuQ04iLCJqdGkiOiI1MGZmMTQwYWU5MWU0NjdiODc3MjNhYzVjYjdmNGM3ZSIsInVzZXIiOiIxMDQxMDg2MjgxNTUwMzExNDI0JDE4NTAxOTIxNTkyJDIiLCJ0eXBlIjoiVVNFUiJ9.IRI8aeKWvmZtHz8mcxAvmzEw-Nzl8osgKrEyR72N4rc'
const {HTTPError, TimeoutError, NetworkError, ParseError, UnsupportedRequestError} = require('./errorTypes')

// 获取命令输入的流式输出
const getOrderStreamResponse = (reqBody, success_cb = (resArr) => {}) => {
  return new Promise((resolve, reject) => {
    const requestTask = wx.request({
      url: HOST+AGENT_ORDER_STREAM_API,
      method: 'POST',
      responseType: 'text',
      data: reqBody,
      enableChunked: true,
      header: {
        'Content-type': 'application/json',
        'Authorization': TOKEN
      },
      timeout: 30000, // 30s未响应为超时
      success: (res) => {
        if (res.statusCode != 200) {
          reject(new HTTPError(res.statusCode, `HTTP Error: ${res.statusCode}`, res));
        }
      },
      fail: (error) => {
        if (error.errMsg.includes('timeout')) {
          reject(new TimeoutError('Request timed out'));
        } else {
          console.error('Network request failed: '+error.errMsg)
          reject(new NetworkError('Network request failed', error));
        }
      }
    });
    let resText = ''
    requestTask.onChunkReceived((chunk) => {
      const arrayBuffer = chunk.data;
      const uint8Array = new Uint8Array(arrayBuffer);
      resText = resText + String.fromCharCode.apply(null, uint8Array);
      // 解码步骤
      let decodedText;
      try {
        decodedText = decodeURIComponent(escape(resText));
      } catch (decodeError) {
        console.error("解码失败:", decodeError);
        return;  // 跳过本轮数据解码，等待下一个块
      }

      try {
          const formatText = `[${decodedText.replace(/}\s*{/g, '},{')}]`;
          const resArr = JSON.parse(formatText);
          resText = ''
          const instruction = resArr.filter((item) => item.instruction).map((item) => {
            //console.log('resArr item: '+item.content)
            return item.instruction
          });
          const finishReason = resArr.filter((item) => item.finish_reason).map((item) => item.finish_reason);
          //console.log('resArr parsed: ' + JSON.stringify(resArr[0]))
          if (instruction.length > 0) {
            success_cb(resArr);
            if (finishReason[0] === 'Done') {
              resolve(instruction[0]) //结束
              return
            }
          }
        } catch (error) {
          console.error("解析错误:", error);
          //reject(error)
        }
      })
  });
}

// 获取对话式流式输入的流式输出
const getDialogStreamResponse = (reqBody, success_cb = (resArr) => {}) => {
  return new Promise((resolve, reject) => {
    const requestTask = wx.request({
      url: HOST+AGENT_DIALOG_STREAM_API,
      method: 'POST',
      responseType: 'text',
      data: reqBody,
      enableChunked: true,
      header: {
        'Content-type': 'application/json',
        'Authorization': TOKEN
      },
      timeout: 30000, // 30s未响应为超时
      success: (res) => {
        if (res.statusCode != 200) {
          reject(new HTTPError(res.statusCode, `HTTP Error: ${res.statusCode}`, res));
        }
      },
      fail: (error) => {
        if (error.errMsg.includes('timeout')) {
          reject(new TimeoutError('Request timed out'));
        } else {
          console.error('Network request failed: '+error.errMsg)
          reject(new NetworkError('Network request failed', error));
        }
      }
    });
    let resText = ''
    requestTask.onChunkReceived((chunk) => {
      console.log('chunk enter')
      const arrayBuffer = chunk.data;
      const uint8Array = new Uint8Array(arrayBuffer);
      resText = resText + String.fromCharCode.apply(null, uint8Array);
      // 解码步骤
      let decodedText;
      try {
        decodedText = decodeURIComponent(escape(resText));
      } catch (decodeError) {
        console.error("解码失败:", decodeError);
        return;  // 跳过本轮数据解码，等待下一个块
      }
      try {
          const formatText = `[${decodedText.replace(/}\s*{/g, '},{')}]`;
          const resArr = JSON.parse(formatText);
          //console.log('resArr: '+resArr)
          resText = ''
          const intent = resArr.filter((item) => item.intent).map((item) => {
            console.log('resArr item: '+item.content)
            return item.intent
          });
          const finishReason = resArr.filter((item) => item.finish_reason).map((item) => item.finish_reason);
          if (intent.length > 0) {
            success_cb(resArr);
            if (finishReason[0] === 'Done') {
              resolve(intent[0]) //结束
              return
            }
          } else {
            reject(new UnsupportedRequestError('不支持'))
          }
        } catch (error) {
          console.error("解析错误:", error);
          //reject(new ParseError("解析错误", error))
        }
    })
  });
}

// 获取所有展项当前状态
const getQueryItems = (reqBody) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: HOST+AGENT_QUERY_ITEMS_API,
      method: 'POST',
      data: reqBody,
      header: {
        'Content-Type': 'application/json',
        'Authorization': TOKEN
        // 如果需要 API 密钥，可以在此处添加，例如 'Authorization': 'Bearer <your-token>'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          console.log('getQueryItems response: '+JSON.stringify(res.data))
          resolve(res.data.data); // 成功时返回助手回复
        } else {
          reject(new Error('未能获取有效回复'));
        }
      },
      fail: (error) => {
        reject(error); // 请求失败时返回错误
      }
    });
  });
}

// 重置聊天记录
const resetHistory = (reqBody) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: HOST+AGENT_RESET_HISTORY_API,
      method: 'POST',
      data: reqBody,
      header: {
        'Content-Type': 'application/json',
        'Authorization': TOKEN
        // 如果需要 API 密钥，可以在此处添加，例如 'Authorization': 'Bearer <your-token>'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          console.log('resetHistory response: '+JSON.stringify(res.data))
          resolve(); // 成功时返回助手回复
        } else {
          reject(new Error('未能获取有效回复'));
        }
      },
      fail: (error) => {
        reject(error); // 请求失败时返回错误
      }
    });
  });
}

// 获取对话相关资源
const getRelatedContent = (reqBody) => {
  console.log('getRelatedContent: '+JSON.stringify(reqBody))
  return new Promise((resolve, reject) => {
    wx.request({
      url: HOST+AGENT_RELATED_CONTENT_API,
      method: 'POST',
      data: reqBody,
      header: {
        'Content-Type': 'application/json',
        'Authorization': TOKEN
        // 如果需要 API 密钥，可以在此处添加，例如 'Authorization': 'Bearer <your-token>'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          console.log('related content response: '+JSON.stringify(res.data))
          resolve(res.data.data); // 成功时返回助手回复
        } else {
          reject(new Error('未能获取有效回复'));
        }
      },
      fail: (error) => {
        reject(error); // 请求失败时返回错误
      }
    });
  });
}

// 获取推荐问题
const getRecommendationQuestions = (reqBody) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: HOST+AGENT_RECOMMENDATION_QUESTIONS_API,
      method: 'POST',
      data: reqBody,
      header: {
        'Content-Type': 'application/json',
        'Authorization': TOKEN
        // 如果需要 API 密钥，可以在此处添加，例如 'Authorization': 'Bearer <your-token>'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          console.log('recommendation questions response: '+JSON.stringify(res.data))
          resolve(res.data.data); // 成功时返回助手回复
        } else {
          reject(new Error('未能获取有效回复'));
        }
      },
      fail: (error) => {
        reject(error); // 请求失败时返回错误
      }
    });
  });
}

// 获取知识问答题目
const getKnowledgeTestQuestions = (reqBody) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: HOST+AGENT_KNOWLEDGE_TEST_QUESTIONS_API,
      method: 'POST',
      data: reqBody,
      header: {
        'Content-Type': 'application/json',
        'Authorization': TOKEN
        // 如果需要 API 密钥，可以在此处添加，例如 'Authorization': 'Bearer <your-token>'
      },
      success: (res) => {
        if (res.data && res.data.code === 200) {
          console.log('knowledge test questions response: '+JSON.stringify(res.data))
          resolve(res.data.data); // 成功时返回助手回复
        } else {
          reject(new Error('未能获取有效回复'));
        }
      },
      fail: (error) => {
        reject(error); // 请求失败时返回错误
      }
    });
  });
}

module.exports = {
  getOrderStreamResponse,
  getDialogStreamResponse,
  getQueryItems,
  resetHistory,
  getRelatedContent,
  getRecommendationQuestions,
  getKnowledgeTestQuestions
}
