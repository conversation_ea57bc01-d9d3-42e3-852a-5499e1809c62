var agentService = require('../../services/agentService')
const sensitiveWords = require('../../assets/js/政治类型')
const app = getApp()
const SpeechSynthesizer = require("../../utils/tts")
const formatTime = require("../../utils/util").formatTime
const getToken = require("../../utils/token").getToken
const fs = wx.getFileSystemManager()
const errorTypes = require('../../services/errorTypes')
const timestamp_key = `debugItemList[0].value`
const duration_key = `debugItemList[1].value`
const item_key = `debugItemList[2].value`
const round_key = `debugItemList[3].value`
const intent_key = `debugItemList[4].value`
let round = 0
let currentItem = ''
let startTimestamp = ''
let hasExit = false
let currentResponseBuffer = ''
let currentChunkContent = ''
let isInitialChunk = true
let lastResponseValid = true
let ctx

Component({
  properties: {
    userPose: null
  },
  data: {
    messages: [], // 消息数组，存放用户和助手的对话内容
    inputText: '', // 当前输入框内容
    userInfo: null,
    isLoading: false,
    isKnowledgeTest: false,
    scrollTop: 0,
    debugItemList: [{
        id: 'timestamp',
        value: '',
        showId: false
      },
      {
        id: 'duration',
        value: '',
        showId: false
      },
      {
        id: 'item',
        value: '',
        showId: true
      },
      {
        id: 'round',
        value: '0',
        showId: true
      },
      {
        id: 'intent',
        value: '',
        showId: true
      }
    ],
    ttsStart: false,
    ttsMessageArray: []
  },
  knowledgeEntry: null,
  knowledgeTestQuestions: null,

  lifetimes: {
    async attached() {
      this.setData({
        userInfo: app.globalData.userInfo
      })
      await this.resetHistory()
      await this.setUpTts()
    },
    detached() {
      this.clearCurrentTts()
    }
  },

  pageLifetimes: {
    // 监听页面尺寸变化，用于横屏适配时重新计算滚动位置
    resize() {
      // 延迟执行，确保样式已经应用
      setTimeout(() => {
        this.handleScrollTop()
      }, 100)
    }
  },

  methods: {
    // 监听输入框输入
    onInput(e) {
      this.setData({
        inputText: e.detail.value
      });
    },

    async setUpTts() {
      try {
        if (!this.data.token) {
          const token = await getToken(app.globalData.AKID,
            app.globalData.AKKEY)
          this.setData({
            token: token
          })
        }
      } catch (e) {
        console.log("error on get token:", JSON.stringify(e))
        return
      }

      let tts = new SpeechSynthesizer({
        url: app.globalData.URL,
        appkey: app.globalData.APPKEY,
        token: this.data.token
      })

      tts.on("data", (msg) => {
        //console.log(`recv size: ${msg.byteLength}`)
        //console.log(dumpFile.write(msg, "binary"))
        if (this.data.saveFile) {
          try {
            fs.appendFileSync(
              this.data.saveFile,
              msg,
              "binary"
            )
            //console.log(`append ${msg.byteLength}`)
          } catch (e) {
            console.error(e)
          }
        } else {
          console.log("save file empty")
        }
      })

      tts.on("completed", async (msg) => {
        if (this.data.saveFd) {
          //console.log("Client recv completed:", msg)
          fs.close({
            fd: this.data.saveFd,
            success: (res) => {
              ctx = wx.createInnerAudioContext()
              ctx.autoplay = true
              ctx.src = this.data.saveFile
              ctx.onPlay(() => {
                console.log('start playing..')
              })
              ctx.onError((res) => {
                console.log('ctx.onError: ' + res.errMsg)
                ctx.destroy()
              })
              ctx.onStop((res) => {
                console.log('ctx stop')
                this.cleanupSaveFile()
                ctx.destroy()
              })
              ctx.onEnded((res) => {
                console.log("play done...")
                this.cleanupSaveFile()
                ctx.destroy()
              })
            },
            fail: (res) => {
              console.log("saved file error:" + res.errMsg)
            }
          })
        } else {
          console.warn("No file descriptor to close");
        }
      })

      tts.on("closed", () => {
        console.log("Client recv closed")
      })

      tts.on("failed", (msg) => {
        console.log("Client recv failed:", msg)
      })

      this.data.tts = tts
    },

    // 发送消息
    async sendMessage() {
      this.clearCurrentTts()
      const {
        inputText,
        messages
      } = this.data;
      if (!inputText.trim()) return;

      // 添加用户消息到对话列表
      const newMessages = [...messages, {
        id: Date.now(),
        content: inputText,
        isUser: true,
        avatarUrl: this.data.userInfo.avatarUrl
      }];
      this.setData({
        messages: newMessages,
        inputText: '' // 清空输入框
      }, async () => {
        await this.handleScrollTop()
        await this.assistantReply(inputText);
        console.log('currentChunkContent:' + currentChunkContent)
        // if (lastResponseValid) {
        //   if (!this.knowledgeTestQuestions) {
        //     this.knowledgeTestQuestions = await this.getKnowledgeTestQuestions()
        //   }
        //   const randomIndex = Math.floor(Math.random() * this.knowledgeTestQuestions.length);
        //   this.knowledgeEntry = this.knowledgeTestQuestions[randomIndex]
        //   const {
        //     question,
        //     choices
        //   } = this.knowledgeEntry
        //   const choicesSplitLines = choices.split('；').map(line => line.trim()).join('\n')
        //   const newMessage = {
        //     id: Date.now(),
        //     content: `问答环节：${question}\n ${choicesSplitLines}`,
        //     isUser: false,
        //     avatarUrl: '/assets/image/agents.png'
        //   }
        //   const newMessages = [...this.data.messages, newMessage]
        //   this.setData({
        //     messages: newMessages,
        //     isKnowledgeTest: true
        //   });
        //   // const relatedContent = await this.getRelatedContent(inputText, currentChunkContent)
        //   // const recommendationQuestions = await this.getRecommendationQuestions(inputText, currentChunkContent)
        // }
      });
    },

    async sendOrderMessage(instruction, project_id, project_name) {
      // const { instruction, project_id, project_name } = e.currentTarget.dataset
      await this.assistantOrderReply(instruction, project_id, project_name)
    },

    async getQueryItems() {
      try {
        const requestBody = {
          user_id: this.data.userInfo.openId
        }
        const result = await agentService.getQueryItems(requestBody)
        // 确保返回的是数组，如果不是则返回空数组
        return Array.isArray(result) ? result : []
      } catch (err) {
        console.error("获取助手回复失败: ", err);
        // 发生错误时返回空数组而不是undefined
        return []
      }
    },

    async resetHistory() {
      try {
        // 重置组件内的全局变量
        round = 0
        currentItem = ''
        startTimestamp = ''
        hasExit = false
        currentResponseBuffer = ''
        currentChunkContent = ''
        isInitialChunk = true
        lastResponseValid = true

        const requestBody = {
          user_id: this.data.userInfo.openId
        }
        return await agentService.resetHistory(requestBody)
      } catch (err) {
        console.error("重置聊天记录失败: ", err);
      }
    },

    async getRelatedContent(queryMessage, response) {
      try {
        const requestBody = {
          user_id: this.data.userInfo.openId,
          user_name: this.data.userInfo.userName,
          query: queryMessage,
          answer: response
        }
        return await agentService.getRelatedContent(requestBody)
      } catch (err) {
        console.error("获取相关多媒体文件失败: ", err);
      }
    },

    async getRecommendationQuestions(queryMessage, response, num = 3) {
      try {
        const requestBody = {
          user_id: this.data.userInfo.openId,
          user_name: this.data.userInfo.userName,
          query: queryMessage,
          answer: response,
          number: num
        }
        return await agentService.getRecommendationQuestions(requestBody)
      } catch (err) {
        console.error("获取推荐问题失败: ", err);
      }
    },

    async getKnowledgeTestQuestions(type = "all") {
      try {
        const requestBody = {
          user_id: this.data.userInfo.openId,
          user_name: this.data.userInfo.userName,
          type: type
        }
        return await agentService.getKnowledgeTestQuestions(requestBody)
      } catch (err) {
        console.error("获取知识问答问题失败: ", err);
      }
    },

    assistantOrderReply(instruction, projectId, projectName) {
      // 只在进入新展项时清除当前TTS，离开展项时不清除
      // if (instruction === 'start') {
      //   this.clearCurrentTts()
      // }

      if (instruction === 'entrance' && projectId && projectName) {
          const newMessage = {
            id: Date.now(),
            content: `您已进入${projectName}`,
            isUser: false,
            avatarUrl: '/assets/image/agents.png'
          }
          this.onTtsStart(newMessage.content)
        const newMessages = [...this.data.messages, newMessage]
        this.setData({
          messages: newMessages
        }, async () => {
          await this.handleScrollTop()
        });
        return
      }
      try {
        const newMessage = {
          id: Date.now(),
          content: '小原思考中',
          isUser: false,
          avatarUrl: '/assets/image/agents.png'
        }
        const newMessages = [...this.data.messages, newMessage]
        startTimestamp = newMessage.id
        this.setData({
          isLoading: true,
          messages: newMessages,
          [timestamp_key]: new Date(startTimestamp).toTimeString().substring(0, 8)
        });
        const index = this.data.messages.length - 1
        const requestBody = {
          user_id: this.data.userInfo.openId, // open id
          user_name: this.data.userInfo.userName,
          instruction: instruction,
          project_id: projectId,
          project_name: projectName
        }
        console.log('orderReply for ' +instruction+': '+projectId)
        return agentService.getOrderStreamResponse(requestBody, (resArr) => this.handleChunkResponse(resArr, index, instruction != 'exit' && instruction != 'stop')).then((instruction) => {
          if (instruction === 'start') {
            currentItem = projectName
          } else if (instruction === 'stop') {
            currentItem = ''
            round = 0
          } else if (instruction === 'exit') {
            hasExit = true
          }
          isInitialChunk = true
          this.setData({
            isLoading: hasExit, // 退场后发送键置灰
            [item_key]: currentItem,
            [round_key]: round
          })
        }).catch(err => {
          this.handleError(err, index)
        })
      } catch (err) {
        console.error("获取助手回复失败: ", err);
      }
    },

    // 模拟助手回复
    assistantReply(userMessage) {
      try {
        const newMessage = {
          id: Date.now(),
          content: '小原思考中',
          isUser: false,
          avatarUrl: '/assets/image/agents.png'
        }
        const newMessages = [...this.data.messages, newMessage]
        startTimestamp = newMessage.id
        this.setData({
          isLoading: true,
          messages: newMessages,
          [timestamp_key]: new Date(startTimestamp).toTimeString().substring(0, 8)
        });
        const index = this.data.messages.length - 1
        const content_key = `messages[${index}].content`
        if (this.containSensitiveWords(userMessage)) {
          const response = '这个话题太敏感, 咱们聊聊展览相关的吧～'
          this.onTtsStart(response)
          this.setData({
            [content_key]: response,
            isLoading: false
          }, async () => {
            await this.handleScrollTop()
          })
          return
        }
        let inputMessage = userMessage
        if (this.data.isKnowledgeTest) {
          const {
            question,
            choices,
            answer,
            description
          } = this.knowledgeEntry
          inputMessage = `趣味问答游戏\n问答题目：${question}\n提供选项：${choices}\n正确答案：${answer}\n用户回答：${userMessage}，\n问题描述：${description}`
        }
        const requestBody = {
          user_id: this.data.userInfo.openId, // open id
          user_name: this.data.userInfo.userName,
          input: inputMessage,
          position: this.data.userPos ? [this.data.userPos.x, this.data.userPos.y, this.data.userPos.z] : []
        }
        return agentService.getDialogStreamResponse(requestBody, (resArr) => this.handleChunkResponse(resArr, index, true)).then((intent) => {
          if (intent === 'leave') {
            this.triggerEvent('leaveIntent')
          } else if (intent === 'question') {
            this.setData({
              isKnowledgeTest: false
            })
          }
          if (currentItem) {
            round += 1
          }
          lastResponseValid = intent === 'qa'
          isInitialChunk = true
          this.setData({
            isLoading: false,
            [round_key]: round,
            [intent_key]: intent
          })
        }).catch(err => {
          this.handleError(err, index)
        })
      } catch (err) {
        console.error("获取助手回复失败: ", err);
      }
    },
    handleError(err, index) {
      const content_key = `messages[${index}].content`
      let response = ''
      if (err instanceof errorTypes.TimeoutError) {
        response = '响应时间过长，可能是网络不好，请重新尝试。'
      } else if (err instanceof errorTypes.HTTPError) {
        response = '后端出小差了，请联系服务器管理员，再重新尝试。' // 开发专属
      } else if (err instanceof errorTypes.NetworkError) {
        response = '网络出小差了，请检查网络配置，再重新尝试。' // 开发专属
      } else if (err instanceof errorTypes.UnsupportedRequestError) {
        response = '不好意思，小原无法回复你的问题。请聊聊展览相关的话题吧。'
      } else if (err instanceof errorTypes.ParseError) {
        response = '不好意思，后端解析失败'
      }
      lastResponseValid = false
      this.onTtsStart(response)
      this.setData({
        [content_key]: response,
        isLoading: false
      }, async () => {
        await this.handleScrollTop()
      })
    },
    handleChunkResponse(resArr, index, ttsOn = true) {
      const content_key = `messages[${index}].content`
      if (isInitialChunk) {
        currentResponseBuffer = ''
        this.setData({
          [content_key]: '',
          [duration_key]: ((Date.now() - startTimestamp) / 1000).toFixed(1) + 's'
        })
        isInitialChunk = false
      }
      //console.log('resArr[0]: '+resArr[0].content)
      const userMessage = resArr.map(item => item.content).join('')
      const isDone = resArr.find(item => item.finish_reason === 'Done') != undefined
      this.splitSentence(userMessage, isDone, ttsOn)
      currentChunkContent = this.data.messages[index].content
      this.setData({
        [content_key]: currentChunkContent + userMessage
      }, async () => {
        await this.handleScrollTop()
      })
    },
    clearCurrentTts() {
      if (!this.data.ttsStart || this.data.isLoading) return
      if (!ctx) {
        console.log('ctx is null')
        return
      }
      console.log('clearCurrentTts')
      ctx.stop()
      this.setData({
        ttsMessageArray: [],
        ttsStart: false,
      })
    },
    splitSentence(userMessage, isDone, ttsOn) {
      let sentence
      currentResponseBuffer += userMessage
      // 查找逗号分隔符
      const sentenceDelimiter = /[；。？！]/;
      let match
      if (isDone) {
        sentence = currentResponseBuffer.trim()
        if (sentence && sentence.length > 0 && ttsOn) {
          this.onTtsStart(sentence)
        }
        currentResponseBuffer = ''
      } else {
        while ((match = currentResponseBuffer.match(sentenceDelimiter)) !== null) {
          // 提取逗号前的完整句子
          const splitIndex = match.index;
          sentence = currentResponseBuffer.slice(0, splitIndex + 1).trim();

          if (sentence && sentence.length > 0 && ttsOn) {
            this.onTtsStart(sentence)
          }

          // 更新 buffer，去掉已处理部分
          currentResponseBuffer = currentResponseBuffer.slice(splitIndex + 1).trim();
        }
      }
    },
    handleScrollTop() {
      return new Promise((resolve, reject) => {
        const query = wx.createSelectorQuery().in(this)
        query.select('#messages-window').boundingClientRect();
        query.select('#scroll-content').boundingClientRect()
        var gap = 20
        query.exec((res) => {
          const scrollViewHeight = res[0].height
          const scrollContentHeight = res[1].height
          if (scrollContentHeight > scrollViewHeight) {
            const scrollTop = scrollContentHeight - scrollViewHeight + gap
            this.setData({
              scrollTop
            }, () => {
              resolve()
            })
          } else {
            resolve()
          }
        })
      })
    },
    containSensitiveWords(inputText) {
      const containsSensitiveWords = sensitiveWords.some(word => inputText.includes(word))
      return containsSensitiveWords
    },
    onTtsStart(ttsMessage) {
      const newMessageArray = [...this.data.ttsMessageArray, ttsMessage]
      this.setData({
        ttsMessageArray: newMessageArray
      })
      this.processTtsMessage(ttsMessage)
    },
    processTtsMessage(ttsMessage) {
      if (!ttsMessage || !this.data.tts) {
        console.log("text empty")
        wx.showToast({
          title: "文本为空",
          icon: "error",
          duration: 1000,
          mask: true
        })
        return
      }
      if (this.data.ttsStart) {
        return
      } else {
        this.setData({
          ttsStart: true
        })
      }
      console.log("try to synthesis:" + ttsMessage)
      let save = formatTime(new Date()) + ".wav"
      let savePath = wx.env.USER_DATA_PATH + "/" + save
      console.log(`save to ${savePath}`)
      fs.open({
        filePath: savePath,
        flag: "a+",
        success: async (res) => {
          console.log(`open ${savePath} done`)
          this.setData({
            saveFile: savePath,
            saveFd: res.fd
          })
          let param = this.data.tts.defaultStartParams()
          param.text = ttsMessage
          param.voice = "zhibei_emo"
          param.speech_rate = 0
          try {
            await this.data.tts.start(param)
            console.log("tts done")
          } catch (e) {
            console.log("tts start error:" + e)
            this.cleanupAfterError()
            return
          }
        },
        fail: (res) => {
          console.log(`open ${savePath} failed: ${res.errMsg}`)
          this.cleanupAfterError()
        }
      })
    },
    cleanupAfterError() {
      this.setData({
        ttsStart: false
      });
      if (this.data.ttsMessageArray.length > 0) {
        const newMessageArray = this.data.ttsMessageArray.slice(1)
        this.setData({
          ttsMessageArray: newMessageArray
        }, () => this.processTtsMessage(this.data.ttsMessageArray[0]))
      }
    },
    cleanupSaveFile() {
      if (this.data.saveFile) {
        fs.unlink({
          filePath: this.data.saveFile,
          success: () => {
            console.log(`Deleted file: ${this.data.saveFile}`);
            this.setData({
              saveFile: null,
              saveFd: null
            })
            const newMessageArray = this.data.ttsMessageArray.slice(1)
            this.setData({
              ttsMessageArray: newMessageArray
            }, () => {
              this.setData({
                ttsStart: false
              }, () => {
                if (newMessageArray.length > 0) {
                  this.processTtsMessage(newMessageArray[0])
                }
              });
            })
          },
          fail: (res) => {
            console.error(`Failed to delete file: ${res.errMsg}`);
          }
        });
      } else {
        console.warn("No save file to delete");
      }
    },
    handleSrInput(evt) {
      const {
        message
      } = evt.detail
      this.setData({
        inputText: message
      })
      this.sendMessage()
    },
    handleSrStart() {
      this.clearCurrentTts()
    }
  }
});